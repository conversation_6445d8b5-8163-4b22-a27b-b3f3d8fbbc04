#!/bin/bash

# Stop and remove existing container if it exists
echo "Stopping and removing existing ACR container..."
docker stop acr 2>/dev/null || true
docker rm acr 2>/dev/null || true

# Remove existing image to force rebuild
echo "Removing existing ACR image..."
docker rmi acr 2>/dev/null || true

# Build new image and run container
echo "Building ACR database image..."
docker build -t acr db

echo "Starting ACR database container..."
docker run --name acr -p 5432:5432 -d acr

echo "ACR database setup complete!"
echo "Database is available at localhost:5432"
echo "Username: postgres"
echo "Password: postgres"
echo "Database: acr"
