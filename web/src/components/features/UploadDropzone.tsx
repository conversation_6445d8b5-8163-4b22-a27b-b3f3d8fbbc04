import { UploadIcon } from "lucide-react";
import { Dropzone, DropzoneEmptyState, DropzoneContent } from "../ui/shadcn-io/dropzone";
import { useState } from "react";
import <PERSON> from "papaparse";

const acceptedFileTypes = {
'text/csv': [], 'application/xml': [], 'application/json': []
};

export type ParsedResult = Record<string, unknown>[];

export const parseFile = async (file: File) => {
  const ext = file.name.split(".").pop()?.toLowerCase();

  if (!ext) throw new Error("Cannot determine file extension");

  if (ext === "csv") {
    return parseCSV(file);
  } else if (ext === "json") {
    return parseJSON(file);
  } else if (ext === "xml") {
    return parseXML(file);
  } else {
    throw new Error(`Unsupported file type: ${ext}`);
  }
};

const parseCSV = (file: File): Promise<ParsedResult> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      complete: (results: any) => resolve(results.data as ParsedResult),
      error: (err: Error) => reject(err),
    });
  });
}

const parseJSON = async (file: File): Promise<ParsedResult> => {
  const text = await file.text();
  const json = JSON.parse(text);
  return Array.isArray(json) ? json : [json];
}

const parseXML = async (file: File): Promise<ParsedResult> => {
  const text = await file.text();
  const parser = new DOMParser();
  const doc = parser.parseFromString(text, "application/xml");

  const contracts = Array.from(doc.getElementsByTagName("contract"));
  const result = contracts.map((c) => ({
    activity_type: c.getElementsByTagName("activity_type")[0]?.textContent ?? "",
    rate: c.getElementsByTagName("rate")[0]?.textContent ?? "",
    currency: c.getElementsByTagName("currency")[0]?.textContent ?? "",
    unit: c.getElementsByTagName("unit")[0]?.textContent ?? "",
    description: c.getElementsByTagName("description")[0]?.textContent ?? "",
  }));
  return result;
}

export const UploadDropzone = () => {
  const [files, setFiles] = useState<File[] | undefined>();
  const handleDrop = async (files: File[]) => {
    console.log(files);
    setFiles(files);

    const parsed = await Promise.all(files.map(parseFile));
    console.log(parsed);
  };

  const handleError = (error: Error) => {
    console.error(error);
  };

  return (
    <Dropzone onDrop={handleDrop} onError={handleError} src={files} className="cursor-pointer" accept={acceptedFileTypes}>
      <DropzoneEmptyState>
        <div className="flex w-full items-center gap-4 p-8">
          <div className="flex size-16 items-center justify-center rounded-lg bg-muted text-muted-foreground">
            <UploadIcon size={24} />
          </div>
          <div className="text-left">
            <p className="font-medium text-sm">Upload a file</p>
            <p className="text-muted-foreground text-xs">
              Drag and drop or click to upload
            </p>
          </div>
        </div>
      </DropzoneEmptyState>
      <DropzoneContent />
    </Dropzone>
  );
};
